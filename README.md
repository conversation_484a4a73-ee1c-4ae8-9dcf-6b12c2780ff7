# Thông tin dự án

Đây là 1 ứng dụng CLI đơn giản được viết bằng Rust phiên bản mới nhất hiện tại, các thư viện và công cụ được sử dụng cho dự án này cũng là những thư viện, công cụ mới nhất và được cộng đồng đánh giá cao và tin tưởng. Ứng dùng này dùng để lắng nghe các giao dịch mới nhất trên blockchain Solana mà trong giao dịch có chứa các địa chỉ ví trong config, và là giao dịch thanh toán của <PERSON>lio (https://www.hel.io/), sau đó giải mã giao dịch để lấy thông tin về người thanh toán và thông báo cho người dùng qua JSON-RPC websocket.

# Luồng hoạt động

1. Kết nối đến dịch vụ grpc transaction streaming
2. Gửi 1 subscribe request đến dịch vụ transaction streaming về các địa chỉ ví sẽ listen
3. Lắng nghe các giao dịch được gửi đến từ server
4. Khi nhận được giao dịch mới, kiểm tra giao dịch đó, nếu đúng là giao dịch theo yêu cầu thì thực hiện giải mã transaction logs, hoặc instructions để lấy các thông tin cần thiết
5. In thông tin về giao dịch đó ra console cũng như gửi cho người dùng qua websocket

# Chức năng chính

-   Hỗ trợ nhiều dịch vụ Transaction Streaming khác nhau (YellowStone Geyser GRPC, Corvus Aurifex RPC (aRPC),...)
-   Hỗ trợ lắng nghe từ nhiều dịch vụ Transaction Streaming cùng 1 lúc, nhằm đảm bảo nhận được giao dịch 1 cách nhanh nhất (vẫn đảm bảo được các giao dịch không bị trùng lặp)
-   Hỗ trợ giải mã giao dịch bằng cả transaction logs hoặc instructions
-   Thông báo qua websocket
-   Tự động generate ra GRPC Client cho các dịch vụ Transaction Streaming từ file proto, chuẩn hóa các class, interface, implement,... để dễ dàng thêm các dịch vụ khác sau này
-   Các file proto sẽ được nhúng vào dự án theo dạng gitsubmodule, liên kết trực tiếp đến repo gốc của dịch vụ, giúp đảm bảo chúng ta luôn có file mới nhất

# Cài đặt và Phát triển

## Yêu cầu hệ thống

-   Rust 1.87.0 hoặc mới hơn
-   Cargo (đi kèm với Rust)

## Cài đặt

```bash
# Clone repository
git clone <repository-url>
cd dexscreener-tracker

# Build dự án
cargo build --release
```

## Sử dụng

### Chạy ứng dụng

```bash
# Chạy ứng dụng
cargo run

# Chạy với verbose output
cargo run -- --verbose

# Hiển thị help
cargo run -- --help
```

### Sử dụng Makefile

Dự án bao gồm Makefile cho các tác vụ phát triển:

```bash
# Hiển thị các lệnh có sẵn
make help

# Build dự án
make build

# Chạy ứng dụng
make run

# Workflow phát triển (format, lint, test)
make dev

# Chạy tất cả kiểm tra (format check, lint, audit, test)
make check

# Format code
make fmt

# Chạy linter
make lint

# Kiểm tra bảo mật
make audit

# Chạy tests
make test
```

## Công cụ chất lượng code

Dự án được cấu hình với các công cụ chất lượng code:

-   **rustfmt**: Format code tự động
-   **clippy**: Linting và static analysis
-   **cargo-audit**: Kiểm tra lỗ hổng bảo mật

## Cấu trúc dự án

```
dexscreener-tracker/
├── src/
│   └── main.rs          # Entry point chính
├── Cargo.toml           # Cấu hình dự án và dependencies
├── .rustfmt.toml        # Cấu hình format Rust
├── clippy.toml          # Cấu hình linting Clippy
├── .gitignore           # Git ignore patterns
├── Makefile             # Lệnh phát triển
└── README.md            # File này
```

# Tài liệu

-   [YellowStone Geyser GRPC Repository](https://github.com/rpcpool/yellowstone-grpc)
-   [YellowStone Geyser GRPC Additional Documentation](https://docs.triton.one/project-yellowstone/dragons-mouth-grpc-subscriptions)
-   [Corvus Aurifex RPC Repository](https://github.com/corvus-labs-io/aurifex)
-   [Codama Repository](https://github.com/codama-idl/codama)
