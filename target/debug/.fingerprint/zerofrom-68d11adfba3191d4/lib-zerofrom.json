{"rustc": 15497389221046826682, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 5347358027863023418, "path": 12260258490245971440, "deps": [[4022439902832367970, "zerofrom_derive", false, 12914421443504604657]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zerofrom-68d11adfba3191d4/dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}