{"rustc": 15497389221046826682, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 740770664831068946, "path": 11826641692747596945, "deps": [[4858255257716900954, "anstyle", false, 12276222840758124120], [11166530783118767604, "strsim", false, 13804208727476119134], [12553266436076736472, "clap_lex", false, 7345463903333048656], [13237942454122161292, "anstream", false, 13722238996387652777]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap_builder-fea28e16d94a5aa0/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}