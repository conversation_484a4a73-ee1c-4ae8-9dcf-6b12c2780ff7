{"rustc": 15497389221046826682, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 740770664831068946, "path": 2821751671897804442, "deps": [[3019522439560520108, "clap_builder", false, 1882144560968106522], [17056525256108235978, "clap_derive", false, 13004764262714418101]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-0f7a49cff08a008c/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}