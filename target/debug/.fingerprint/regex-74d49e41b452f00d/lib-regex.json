{"rustc": 15497389221046826682, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5347358027863023418, "path": 15033791335116528145, "deps": [[555019317135488525, "regex_automata", false, 8861329024951605345], [2779309023524819297, "aho_corasick", false, 735703182898872885], [3129130049864710036, "memchr", false, 3312179632490387645], [9408802513701742484, "regex_syntax", false, 8249900930247568180]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-74d49e41b452f00d/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}