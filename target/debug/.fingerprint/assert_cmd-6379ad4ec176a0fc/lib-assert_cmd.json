{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"color\", \"color-auto\"]", "target": 16602908775176716730, "profile": 16450813782370704554, "path": 10010589475145802646, "deps": [[904119603456001782, "bstr", false, 9619609616880098417], [4858255257716900954, "anstyle", false, 12276222840758124120], [6491540798839599208, "predicates_core", false, 15856858174061182231], [12516616738327129663, "predicates_tree", false, 2521579063368469606], [12939671402123591185, "build_script_build", false, 1088051337354586008], [15863765456528386755, "predicates", false, 2587138688290830245], [17492147245553934378, "wait_timeout", false, 6926767163834183377], [18000218614148971598, "doc_comment", false, 3740359288199043299]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/assert_cmd-6379ad4ec176a0fc/dep-lib-assert_cmd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}