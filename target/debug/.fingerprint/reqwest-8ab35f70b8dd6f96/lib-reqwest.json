{"rustc": 15497389221046826682, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 2383083043656166682, "path": 11421806723646062975, "deps": [[40386456601120721, "percent_encoding", false, 11021943286677184330], [95042085696191081, "ipnet", false, 13825495589799560805], [418947936956741439, "h2", false, 11628918308666345746], [784494742817713399, "tower_service", false, 7067141016842501417], [970965535607393401, "hyper_util", false, 14773591732571081012], [1475350004718460454, "tower_http", false, 4926935465103769561], [1906322745568073236, "pin_project_lite", false, 10604144968048648353], [2517136641825875337, "sync_wrapper", false, 12250473889869910410], [2883436298747778685, "rustls_pki_types", false, 15320373550301187232], [3150220818285335163, "url", false, 14252225793551598665], [3722963349756955755, "once_cell", false, 263002596821618790], [5695049318159433696, "tower", false, 2637520467335328788], [5986029879202738730, "log", false, 3889190429195040124], [7620660491849607393, "futures_core", false, 2045025667838836927], [9010263965687315507, "http", false, 1154442078700790027], [9538054652646069845, "tokio", false, 1650825800493031450], [9689903380558560274, "serde", false, 11079311394798989866], [10229185211513642314, "mime", false, 7335903177883505045], [11957360342995674422, "hyper", false, 14838894167789928383], [12186126227181294540, "tokio_native_tls", false, 6460293242274264023], [13077212702700853852, "base64", false, 17880816446081076803], [14084095096285906100, "http_body", false, 4639676682145272357], [14564311161534545801, "encoding_rs", false, 16727783803130639954], [15367738274754116744, "serde_json", false, 15287885097095879286], [16066129441945555748, "bytes", false, 1910611532240841935], [16542808166767769916, "serde_urlencoded", false, 6904289667412176708], [16785601910559813697, "native_tls_crate", false, 2379286869750987014], [16900715236047033623, "http_body_util", false, 14984100002862575355], [18273243456331255970, "hyper_tls", false, 15986929138713027471]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-8ab35f70b8dd6f96/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}