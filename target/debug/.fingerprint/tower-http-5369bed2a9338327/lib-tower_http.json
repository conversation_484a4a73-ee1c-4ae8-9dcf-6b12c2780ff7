{"rustc": 15497389221046826682, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 5347358027863023418, "path": 5378637503109322627, "deps": [[784494742817713399, "tower_service", false, 7067141016842501417], [1906322745568073236, "pin_project_lite", false, 10604144968048648353], [4121350475192885151, "iri_string", false, 13247499546185973416], [5695049318159433696, "tower", false, 2637520467335328788], [7712452662827335977, "tower_layer", false, 16585682252649650789], [7896293946984509699, "bitflags", false, 15968665499601520469], [9010263965687315507, "http", false, 1154442078700790027], [10629569228670356391, "futures_util", false, 4767363480226270355], [14084095096285906100, "http_body", false, 4639676682145272357], [16066129441945555748, "bytes", false, 1910611532240841935]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-5369bed2a9338327/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}