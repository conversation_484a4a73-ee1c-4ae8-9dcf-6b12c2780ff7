# Makefile for DexScreener Tracker
# Development commands for Rust CLI project

.PHONY: help build run test clean fmt lint audit check install dev

# Default target
help:
	@echo "Available commands:"
	@echo "  build    - Build the project"
	@echo "  run      - Run the application"
	@echo "  test     - Run tests"
	@echo "  clean    - Clean build artifacts"
	@echo "  fmt      - Format code with rustfmt"
	@echo "  lint     - Run clippy linter"
	@echo "  audit    - Run security audit"
	@echo "  check    - Run all checks (fmt, lint, audit, test)"
	@echo "  install  - Install the binary"
	@echo "  dev      - Development mode (format, lint, test)"

# Build the project
build:
	cargo build

# Build release version
build-release:
	cargo build --release

# Run the application
run:
	cargo run

# Run with arguments (usage: make run-args ARGS="--verbose")
run-args:
	cargo run -- $(ARGS)

# Run tests
test:
	cargo test

# Clean build artifacts
clean:
	cargo clean

# Format code
fmt:
	cargo fmt

# Check formatting without making changes
fmt-check:
	cargo fmt -- --check

# Run clippy linter
lint:
	cargo clippy -- -D warnings

# Run security audit
audit:
	cargo audit

# Run all checks
check: fmt-check lint audit test
	@echo "All checks passed!"

# Install the binary
install:
	cargo install --path .

# Development workflow
dev: fmt lint test
	@echo "Development checks completed!"

# Update dependencies
update:
	cargo update

# Show project info
info:
	@echo "Project: dexscreener-tracker"
	@echo "Rust version: $(shell rustc --version)"
	@echo "Cargo version: $(shell cargo --version)"
	@echo "Dependencies:"
	@cargo tree --depth 1
