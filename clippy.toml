# Clippy configuration for code quality and linting
# Based on project coding guidelines for maintaining high code quality

# Complexity limits
cognitive-complexity-threshold = 30
type-complexity-threshold = 250
too-many-arguments-threshold = 7
too-many-lines-threshold = 100

# Naming conventions
enum-variant-name-threshold = 3
max-struct-bools = 3

# Documentation requirements
doc-valid-idents = ["DexScreener", "CLI", "API", "JSON", "HTTP", "URL", "UUID"]

# Performance and efficiency
trivial-copy-size-limit = 128
pass-by-value-size-limit = 256
array-size-threshold = 512000

# Code style preferences
single-char-binding-names-threshold = 4
literal-representation-threshold = 10

# Error handling
large-error-threshold = 128

# Avoid certain patterns
avoid-breaking-exported-api = true
msrv = "1.70.0"

# Allow certain clippy lints that might be too strict
allow-expect-in-tests = true
allow-unwrap-in-tests = true
allow-dbg-in-tests = true
allow-print-in-tests = true
