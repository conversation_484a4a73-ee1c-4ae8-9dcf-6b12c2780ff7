# Rust build artifacts
/target/
Cargo.lock

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Rust-specific
**/*.rs.bk
*.pdb

# Backup files
*.bak
*.backup
*.tmp

# Local configuration
config.local.*
.local/

# Test artifacts
test-results/
*.test

# Documentation build
book/

# Flamegraph profiling
flamegraph.svg
perf.data*
